#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 q_1208.ts 文件使用 TetGen 的 'pY' 参数进行表面保持四面体化
检查网格是否闭合以及四面体化结果
"""

import numpy as np
import os
import sys
import time
from typing import Tuple, List, Optional

def try_pyvista_tetrahedralization(vertices: np.ndarray, faces: np.ndarray) -> Optional[object]:
    """
    使用 PyVista 进行四面体化作为备选方案

    Args:
        vertices: 顶点数组
        faces: 面数组

    Returns:
        四面体网格对象或None
    """
    print("\n=== PyVista 四面体化备选方案 ===")

    try:
        import pyvista as pv
        print("✓ PyVista 库可用")
    except ImportError as e:
        print(f"✗ PyVista 库不可用: {e}")
        return None

    try:
        # 创建PyVista表面网格
        print("创建 PyVista 表面网格...")
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        faces_array = np.array(faces_with_header)

        surface_mesh = pv.PolyData(vertices, faces_array)
        print(f"表面网格: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")

        # 尝试修复网格
        print("尝试修复网格...")
        try:
            # 移除重复点
            cleaned_mesh = surface_mesh.clean()
            print(f"清理后: {cleaned_mesh.n_points} 顶点, {cleaned_mesh.n_cells} 面")

            # 填充孔洞
            filled_mesh = cleaned_mesh.fill_holes(hole_size=1000.0)
            print(f"填充孔洞后: {filled_mesh.n_points} 顶点, {filled_mesh.n_cells} 面")

            surface_mesh = filled_mesh
        except Exception as e:
            print(f"网格修复失败: {e}")

        # 尝试使用PyVista的四面体化
        print("尝试 PyVista 四面体化...")
        try:
            # 方法1: 使用delaunay_3d
            tetrahedral_mesh = surface_mesh.delaunay_3d()

            if tetrahedral_mesh.n_cells > 0:
                print(f"✓ PyVista 四面体化成功")
                print(f"输出网格: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                return tetrahedral_mesh
            else:
                raise ValueError("四面体化结果为空")

        except Exception as e:
            print(f"✗ PyVista 四面体化失败: {e}")
            return None

    except Exception as e:
        print(f"✗ PyVista 测试过程中发生错误: {e}")
        return None

def read_tsurf_data(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    读取 GOCAD TSurf 格式文件
    
    Args:
        file_path: TS文件路径
        
    Returns:
        vertices: 顶点数组 (N, 3)
        faces: 面数组 (M, 3)
    """
    print(f"正在读取 TS 文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vertices = []
            vertex_map = {}  # 用于映射顶点ID到索引
            faces = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    if line.startswith('VRTX'):
                        # 解析顶点: VRTX id x y z
                        parts = line.split()
                        if len(parts) >= 5:
                            vertex_id = int(parts[1])
                            x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                            vertices.append([x, y, z])
                            vertex_map[vertex_id] = current_idx
                            current_idx += 1
                    
                    elif line.startswith('TRGL'):
                        # 解析三角形: TRGL v1 v2 v3
                        parts = line.split()
                        if len(parts) >= 4:
                            v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                            if v1 in vertex_map and v2 in vertex_map and v3 in vertex_map:
                                faces.append([vertex_map[v1], vertex_map[v2], vertex_map[v3]])
            
            vertices = np.array(vertices, dtype=np.float64)
            faces = np.array(faces, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            print(f"使用编码: {encoding}")
            
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用编码 {encoding} 读取失败: {e}")
            continue
    
    raise ValueError(f"无法读取文件 {file_path}，尝试了所有编码")

def check_mesh_closure(vertices: np.ndarray, faces: np.ndarray) -> dict:
    """
    检查网格是否闭合
    
    Args:
        vertices: 顶点数组
        faces: 面数组
        
    Returns:
        检查结果字典
    """
    print("\n=== 网格闭合性检查 ===")
    
    # 统计边的使用次数
    edge_count = {}
    
    for face in faces:
        # 每个三角形有3条边
        edges = [
            tuple(sorted([face[0], face[1]])),
            tuple(sorted([face[1], face[2]])),
            tuple(sorted([face[2], face[0]]))
        ]
        
        for edge in edges:
            edge_count[edge] = edge_count.get(edge, 0) + 1
    
    # 分析边的使用情况
    boundary_edges = []  # 只被一个面使用的边（边界边）
    internal_edges = []  # 被两个面使用的边（内部边）
    problematic_edges = []  # 被超过两个面使用的边（问题边）
    
    for edge, count in edge_count.items():
        if count == 1:
            boundary_edges.append(edge)
        elif count == 2:
            internal_edges.append(edge)
        else:
            problematic_edges.append(edge)
    
    is_closed = len(boundary_edges) == 0
    
    result = {
        'is_closed': is_closed,
        'total_edges': len(edge_count),
        'boundary_edges': len(boundary_edges),
        'internal_edges': len(internal_edges),
        'problematic_edges': len(problematic_edges),
        'boundary_edge_list': boundary_edges[:10] if boundary_edges else [],  # 只显示前10个
        'problematic_edge_list': problematic_edges[:10] if problematic_edges else []
    }
    
    print(f"网格是否闭合: {'是' if is_closed else '否'}")
    print(f"总边数: {result['total_edges']}")
    print(f"边界边数: {result['boundary_edges']}")
    print(f"内部边数: {result['internal_edges']}")
    print(f"问题边数: {result['problematic_edges']}")
    
    if boundary_edges:
        print(f"前10个边界边: {result['boundary_edge_list']}")
    
    if problematic_edges:
        print(f"前10个问题边: {result['problematic_edge_list']}")
    
    return result

def test_tetgen_tetrahedralization(vertices: np.ndarray, faces: np.ndarray) -> Optional[object]:
    """
    使用 TetGen 的 'pY' 参数进行表面保持四面体化测试

    Args:
        vertices: 顶点数组
        faces: 面数组

    Returns:
        四面体网格对象或None
    """
    print("\n=== TetGen 表面保持四面体化测试 ===")

    try:
        import tetgen
        print("✓ TetGen 库可用")
    except ImportError as e:
        print(f"✗ TetGen 库不可用: {e}")
        print("请安装 tetgen: pip install tetgen")
        return None

    try:
        print("创建 TetGen 对象...")
        tet = tetgen.TetGen(vertices, faces)

        print("网格信息:")
        print(f"  输入顶点数: {len(vertices)}")
        print(f"  输入面数: {len(faces)}")

        # 检查输入网格的基本属性
        print(f"  顶点坐标范围:")
        print(f"    X: [{vertices[:, 0].min():.2f}, {vertices[:, 0].max():.2f}]")
        print(f"    Y: [{vertices[:, 1].min():.2f}, {vertices[:, 1].max():.2f}]")
        print(f"    Z: [{vertices[:, 2].min():.2f}, {vertices[:, 2].max():.2f}]")

        # 尝试修复网格流形结构
        print("\n尝试修复网格流形结构...")
        try:
            tet.make_manifold(verbose=True)
            print("✓ 网格流形修复完成")
        except Exception as e:
            print(f"⚠️ 网格流形修复失败: {e}")

        # 尝试多种TetGen参数组合
        test_params = [
            ('pY', '表面保持四面体化'),
            ('pYq1.2', '表面保持 + 质量约束'),
            ('pYq1.5', '表面保持 + 宽松质量约束'),
            ('pq1.2', '质量约束四面体化'),
            ('p', '基本四面体化')
        ]

        for params, description in test_params:
            print(f"\n尝试参数: '{params}' ({description})...")
            start_time = time.time()

            try:
                tet.tetrahedralize(params)
                end_time = time.time()
                print(f"✓ 四面体化成功，耗时: {end_time - start_time:.2f} 秒")

                # 获取结果网格
                tetrahedral_mesh = tet.grid

                print("四面体化结果:")
                print(f"  输出顶点数: {tetrahedral_mesh.n_points}")
                print(f"  输出四面体数: {tetrahedral_mesh.n_cells}")

                # 检查结果网格的质量
                if hasattr(tetrahedral_mesh, 'compute_cell_quality'):
                    try:
                        quality = tetrahedral_mesh.compute_cell_quality()
                        print(f"  四面体质量统计:")
                        print(f"    最小质量: {quality.min():.6f}")
                        print(f"    最大质量: {quality.max():.6f}")
                        print(f"    平均质量: {quality.mean():.6f}")
                    except Exception as e:
                        print(f"  无法计算四面体质量: {e}")

                # 提取表面网格并检查是否保持了原始表面
                print("\n检查表面保持情况...")
                surface_mesh = tetrahedral_mesh.extract_surface()
                print(f"  提取的表面顶点数: {surface_mesh.n_points}")
                print(f"  提取的表面面数: {surface_mesh.n_cells}")

                # 检查表面网格是否闭合
                if surface_mesh.n_cells > 0:
                    # 获取表面网格的面数据
                    surface_faces = []
                    for i in range(surface_mesh.n_cells):
                        cell = surface_mesh.get_cell(i)
                        if cell.n_points == 3:  # 三角形面
                            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

                    if surface_faces:
                        surface_faces = np.array(surface_faces)
                        surface_vertices = surface_mesh.points

                        print("\n检查输出表面网格的闭合性...")
                        surface_closure = check_mesh_closure(surface_vertices, surface_faces)

                        if surface_closure['is_closed']:
                            print("✓ 输出表面网格是闭合的")
                        else:
                            print("✗ 输出表面网格不是闭合的")

                return tetrahedral_mesh

            except Exception as e:
                print(f"✗ 参数 '{params}' 失败: {e}")
                continue

        print("✗ 所有TetGen参数都失败了")
        return None

    except Exception as e:
        print(f"✗ TetGen 测试过程中发生错误: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("q_1208.ts TetGen 表面保持四面体化测试")
    print("=" * 60)
    
    # 检查文件是否存在
    ts_file = "q_1208.ts"
    if not os.path.exists(ts_file):
        print(f"错误: 文件 {ts_file} 不存在")
        return
    
    try:
        # 1. 读取 TS 文件
        vertices, faces = read_tsurf_data(ts_file)
        
        if len(vertices) == 0 or len(faces) == 0:
            print("错误: 无法从文件中读取有效的网格数据")
            return
        
        # 2. 检查输入网格的闭合性
        input_closure = check_mesh_closure(vertices, faces)
        
        # 3. 进行 TetGen 表面保持四面体化测试
        tetrahedral_mesh = test_tetgen_tetrahedralization(vertices, faces)

        # 4. 如果TetGen失败，尝试PyVista备选方案
        pyvista_mesh = None
        if tetrahedral_mesh is None:
            pyvista_mesh = try_pyvista_tetrahedralization(vertices, faces)

        # 5. 总结结果
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        print(f"输入网格: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"输入网格闭合性: {'闭合' if input_closure['is_closed'] else '不闭合'}")

        if input_closure['boundary_edges'] > 0:
            print(f"输入网格边界边数: {input_closure['boundary_edges']}")

        if tetrahedral_mesh is not None:
            print(f"✓ TetGen 四面体化成功")
            print(f"输出网格: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
        else:
            print("✗ TetGen 四面体化失败")

        if pyvista_mesh is not None:
            print(f"✓ PyVista 四面体化成功")
            print(f"输出网格: {pyvista_mesh.n_points} 顶点, {pyvista_mesh.n_cells} 四面体")
        elif tetrahedral_mesh is None:
            print("✗ PyVista 四面体化也失败")

        # 6. 分析失败原因和建议
        if tetrahedral_mesh is None and pyvista_mesh is None:
            print("\n失败原因分析:")
            print("- 输入网格不是闭合的流形")
            print("- 存在大量边界边，表明网格有孔洞或不连续")
            print("\n建议:")
            print("1. 使用专业的网格修复工具修复输入网格")
            print("2. 检查原始数据是否完整")
            print("3. 考虑使用其他四面体化算法")
            print("4. 安装 pymeshfix 进行网格修复: pip install pymeshfix")

        print("\n测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
